// First install: npm install @react-pdf/renderer

import React from 'react';
import { Document, Page, Text, View, StyleSheet, pdf } from '@react-pdf/renderer';

// Create styles
const styles = StyleSheet.create({
  page: {
    flexDirection: 'column',
    backgroundColor: '#FFFFFF',
    padding: 30,
    fontFamily: 'Helvetica',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 30,
  },
  companyInfo: {
    flex: 1,
  },
  companyName: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 5,
  },
  companyDetails: {
    fontSize: 9,
    lineHeight: 1.4,
  },
  invoiceTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    textAlign: 'right',
    marginBottom: 10,
  },
  invoiceDetails: {
    fontSize: 10,
    textAlign: 'right',
  },
  billTo: {
    marginBottom: 20,
  },
  billToTitle: {
    fontSize: 12,
    fontWeight: 'bold',
    marginBottom: 5,
  },
  billToDetails: {
    fontSize: 10,
    lineHeight: 1.4,
  },
  table: {
    marginBottom: 20,
  },
  tableHeader: {
    flexDirection: 'row',
    backgroundColor: '#ADD8E6',
    borderWidth: 1,
    borderColor: '#000000',
  },
  tableRow: {
    flexDirection: 'row',
    borderWidth: 1,
    borderColor: '#000000',
    borderTopWidth: 0,
  },
  tableCell: {
    padding: 5,
    fontSize: 10,
    borderRightWidth: 1,
    borderRightColor: '#000000',
  },
  tableCellHeader: {
    padding: 5,
    fontSize: 10,
    fontWeight: 'bold',
    borderRightWidth: 1,
    borderRightColor: '#000000',
  },
  totals: {
    alignSelf: 'flex-end',
    width: 200,
    marginBottom: 20,
  },
  totalRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 5,
    fontSize: 10,
  },
  footer: {
    marginTop: 'auto',
    fontSize: 9,
    lineHeight: 1.4,
  },
});

interface InvoiceData {
  paymentId: string;
  amount: string;
  date: string;
  customerName: string;
  customerEmail: string;
  packageName: string;
  price: string;
}

const InvoicePDF: React.FC<{ invoiceData: InvoiceData }> = ({ invoiceData }) => (
  <Document>
    <Page size="A4" style={styles.page}>
      {/* Header */}
      <View style={styles.header}>
        <View style={styles.companyInfo}>
          <Text style={styles.companyName}>Macgence</Text>
          <View style={styles.companyDetails}>
            <Text>Macgence Technologies Private Limited</Text>
            <Text>CN UF22000UP2022PTC164392</Text>
            <Text>7th Floor, Platina Heights C - 24 Sector 62</Text>
            <Text>Noida Uttar Pradesh 201301</Text>
            <Text>India</Text>
            <Text>GSTIN:09AAPCH4735A1Z9</Text>
          </View>
        </View>
        
        <View>
          <Text style={styles.invoiceTitle}>TAX INVOICE</Text>
          <View style={styles.invoiceDetails}>
            <Text>Invoice# INV25/26-00021</Text>
            <Text style={{ marginTop: 10, fontWeight: 'bold' }}>Balance Due</Text>
            <Text style={{ fontSize: 16, fontWeight: 'bold' }}>${invoiceData.amount}</Text>
            <Text style={{ marginTop: 10 }}>Invoice Date: {new Date(invoiceData.date).toLocaleDateString('en-GB')}</Text>
            <Text>Terms: Net 4</Text>
            <Text>Due Date: {new Date(new Date(invoiceData.date).getTime() + 4 * 24 * 60 * 60 * 1000).toLocaleDateString('en-GB')}</Text>
          </View>
        </View>
      </View>

      {/* Bill To */}
      <View style={styles.billTo}>
        <Text style={styles.billToTitle}>Bill To</Text>
        <View style={styles.billToDetails}>
          <Text>{invoiceData.customerName}</Text>
          <Text>{invoiceData.customerEmail}</Text>
          <Text>Israel</Text>
        </View>
      </View>

      {/* Table */}
      <View style={styles.table}>
        <View style={styles.tableHeader}>
          <Text style={[styles.tableCellHeader, { width: '8%' }]}>#</Text>
          <Text style={[styles.tableCellHeader, { width: '40%' }]}>Item & Description</Text>
          <Text style={[styles.tableCellHeader, { width: '15%' }]}>HSN/SAC</Text>
          <Text style={[styles.tableCellHeader, { width: '12%' }]}>Qty</Text>
          <Text style={[styles.tableCellHeader, { width: '12%' }]}>Rate</Text>
          <Text style={[styles.tableCellHeader, { width: '13%', borderRightWidth: 0 }]}>Amount</Text>
        </View>
        
        <View style={styles.tableRow}>
          <Text style={[styles.tableCell, { width: '8%' }]}>1</Text>
          <Text style={[styles.tableCell, { width: '40%' }]}>B2B Invoices dataset delivery{'\n'}(OTS Inventory)</Text>
          <Text style={[styles.tableCell, { width: '15%' }]}>998313</Text>
          <Text style={[styles.tableCell, { width: '12%' }]}>1,000.00</Text>
          <Text style={[styles.tableCell, { width: '12%' }]}>0.30</Text>
          <Text style={[styles.tableCell, { width: '13%', borderRightWidth: 0 }]}>300.00</Text>
        </View>
      </View>

      {/* Totals */}
      <View style={styles.totals}>
        <View style={styles.totalRow}>
          <Text>Sub Total</Text>
          <Text>300.00</Text>
        </View>
        <View style={[styles.totalRow, { fontWeight: 'bold', borderTopWidth: 1, borderTopColor: '#000', paddingTop: 5 }]}>
          <Text>Total</Text>
          <Text>${invoiceData.amount}</Text>
        </View>
        <View style={[styles.totalRow, { fontWeight: 'bold' }]}>
          <Text>Balance Due</Text>
          <Text>${invoiceData.amount}</Text>
        </View>
        
        <View style={{ marginTop: 15 }}>
          <Text style={{ fontSize: 9, fontWeight: 'bold' }}>Total In Words:</Text>
          <Text style={{ fontSize: 9, fontStyle: 'italic' }}>United States Dollar Three</Text>
          <Text style={{ fontSize: 9, fontStyle: 'italic' }}>Hundred</Text>
        </View>
      </View>

      {/* Footer */}
      <View style={styles.footer}>
        <Text>Thanks for your business.</Text>
        <Text>PayPal account: <EMAIL></Text>
        <Text style={{ marginTop: 10 }}>Account Holder Name : Macgence Technologies Private Limited</Text>
        <Text>Payment Method : SWIFT (International Wire)</Text>
        <Text>IBAN : **********************</Text>
        <Text>BIC/SWIFT : TCCLGB3L</Text>
        <Text>Bank Name : The Currency Cloud Limited</Text>
        <Text>Bank Address : 12, Steward Street, The Steward Building, London, E1 6FQ, Great Britain, United Kingdom</Text>
      </View>
    </Page>
  </Document>
);

export const generateInvoiceWithReactPdf = async (invoiceData: InvoiceData) => {
  const blob = await pdf(<InvoicePDF invoiceData={invoiceData} />).toBlob();
  const url = URL.createObjectURL(blob);
  const link = document.createElement('a');
  link.href = url;
  link.download = `TAX_INVOICE_${invoiceData.paymentId}.pdf`;
  link.click();
  URL.revokeObjectURL(url);
};
