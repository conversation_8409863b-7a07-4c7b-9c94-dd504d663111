import React from 'react';

interface InvoiceTemplateProps {
  invoiceData: {
    paymentId: string;
    amount: string;
    date: string;
    customerName: string;
    customerEmail: string;
    packageName: string;
    price: string;
  };
}

export const InvoiceTemplate: React.FC<InvoiceTemplateProps> = ({ invoiceData }) => {
  return (
    <div className="w-[210mm] h-[297mm] bg-white p-8 font-sans text-black" style={{ fontSize: '12px' }}>
      {/* Header Section */}
      <div className="flex justify-between items-start mb-8">
        {/* Left Side - Company Details */}
        <div className="flex-1">
          <div className="mb-4">
            <img src="/src/assets/darklogo.png" alt="Macgence Logo" className="h-12 mb-2" />
          </div>
          <div className="text-lg font-bold mb-2">Macgence</div>
          <div className="text-sm space-y-1">
            <div>Macgence Technologies Private Limited</div>
            <div>CN UF22000UP2022PTC164392</div>
            <div>7th Floor, Platina Heights C - 24 Sector 62</div>
            <div>Noida Uttar Pradesh 201301</div>
            <div>India</div>
            <div>GSTIN:09AAPCH4735A1Z9</div>
          </div>
        </div>

        {/* Right Side - Invoice Details */}
        <div className="text-right">
          <h1 className="text-3xl font-bold mb-4">TAX INVOICE</h1>
          <div className="text-sm space-y-2">
            <div>Invoice# INV25/26-00021</div>
            <div className="mt-4">
              <div className="font-semibold">Balance Due</div>
              <div className="text-xl font-bold">${invoiceData.amount}</div>
            </div>
            <div className="mt-4 space-y-1">
              <div className="flex justify-between">
                <span>Invoice Date :</span>
                <span>{new Date(invoiceData.date).toLocaleDateString('en-GB')}</span>
              </div>
              <div className="flex justify-between">
                <span>Terms :</span>
                <span>Net 4</span>
              </div>
              <div className="flex justify-between">
                <span>Due Date :</span>
                <span>{new Date(new Date(invoiceData.date).getTime() + 4 * 24 * 60 * 60 * 1000).toLocaleDateString('en-GB')}</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Bill To Section */}
      <div className="mb-8">
        <div className="font-bold text-sm mb-2">Bill To</div>
        <div className="text-sm space-y-1">
          <div>{invoiceData.customerName}</div>
          <div>{invoiceData.customerEmail}</div>
          <div>Israel</div>
        </div>
      </div>

      {/* Invoice Table */}
      <div className="mb-8">
        <table className="w-full border-collapse border border-black">
          <thead>
            <tr className="bg-blue-200">
              <th className="border border-black p-2 text-left text-sm font-bold w-8">#</th>
              <th className="border border-black p-2 text-left text-sm font-bold">Item & Description</th>
              <th className="border border-black p-2 text-left text-sm font-bold w-20">HSN/SAC</th>
              <th className="border border-black p-2 text-left text-sm font-bold w-20">Qty</th>
              <th className="border border-black p-2 text-left text-sm font-bold w-16">Rate</th>
              <th className="border border-black p-2 text-left text-sm font-bold w-20">Amount</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td className="border border-black p-2 text-sm">1</td>
              <td className="border border-black p-2 text-sm">
                <div>B2B Invoices dataset delivery</div>
                <div>(OTS Inventory)</div>
              </td>
              <td className="border border-black p-2 text-sm">998313</td>
              <td className="border border-black p-2 text-sm">1,000.00</td>
              <td className="border border-black p-2 text-sm">0.30</td>
              <td className="border border-black p-2 text-sm">300.00</td>
            </tr>
          </tbody>
        </table>
      </div>

      {/* Totals Section */}
      <div className="flex justify-end mb-8">
        <div className="w-64">
          <div className="space-y-2 text-sm">
            <div className="flex justify-between">
              <span>Sub Total</span>
              <span>300.00</span>
            </div>
            <div className="flex justify-between font-bold border-t pt-2">
              <span>Total</span>
              <span>${invoiceData.amount}</span>
            </div>
            <div className="flex justify-between font-bold">
              <span>Balance Due</span>
              <span>${invoiceData.amount}</span>
            </div>
          </div>
          
          <div className="mt-4 text-sm">
            <div className="font-semibold">Total In Words:</div>
            <div className="italic">United States Dollar Three</div>
            <div className="italic">Hundred</div>
          </div>
        </div>
      </div>

      {/* Footer */}
      <div className="mt-auto text-sm space-y-1">
        <div>Thanks for your business.</div>
        <div>PayPal account: <EMAIL></div>
        
        <div className="mt-4 space-y-1">
          <div>Account Holder Name : Macgence Technologies Private Limited</div>
          <div>Payment Method : SWIFT (International Wire)</div>
          <div>IBAN : **********************</div>
          <div>BIC/SWIFT : TCCLGB3L</div>
          <div>Bank Name : The Currency Cloud Limited</div>
          <div>Bank Address : 12, Steward Street, The Steward Building, London, E1 6FQ, Great Britain, United Kingdom</div>
        </div>
      </div>
    </div>
  );
};
