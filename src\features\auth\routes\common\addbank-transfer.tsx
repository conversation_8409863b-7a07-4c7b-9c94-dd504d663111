// import img1 from "@/assets/darklogo.png";
import { useForm } from "react-hook-form";
import { But<PERSON> } from "@/components/ui/button";
import {
  Form,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  FormControl,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import FileUpload from "../common/file-uplaod";
import { useLocation, useNavigate } from "react-router-dom";
import { PostBankTransfer } from "../banktransfer-api/banktransfer-api";
import { toast } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import { useState } from "react";

// Form Schema
const formSchema = z.object({
  transactionId: z.string().min(1, "Transaction ID is required"),
  AccountName: z
    .string()
    .min(3, "Holder name is required")
    .max(20, "Account name is maximum 20 words limit")
    .regex(/^[A-Za-z\s]+$/, "Only letters and spaces are allowed"),
  amount: z
    .string()
    .min(1, "Amount is required")
    .max(20, "Amount is maximum number")
    .regex(/^\d+$/, "Only numbers are allowed"),
  transactionDate: z
    .string()
    .min(1, "Transaction date is required")
    .regex(/^\d{4}-\d{2}-\d{2}$/, "Date must be in YYYY-MM-DD format")
    .refine((val) => !isNaN(Date.parse(val)), "Invalid date format"),
  transferbankaccount: z.string().min(1, "Transfer bank account is required"),
  accountNumber: z.string().optional(), // Make accountNumber optional
  document: z
    .instanceof(File)
    .refine((file) => file.size > 0, "Please upload a file")
    .refine(
      (file) => file.size <= 5 * 1024 * 1024,
      "File size must be less than 5MB"
    )
    .refine(
      (file) =>
        ["image/jpeg", "image/png", "image/jpg", "application/pdf"].includes(
          file.type
        ),
      "Only JPEG, PNG, JPG, or PDF files are allowed"
    ),
});

const AddBankTransfer = () => {
  const { state } = useLocation();
  const navigate = useNavigate();
  const [isFileDialogOpen, setIsFileDialogOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false); // Add loading state

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      transactionId: "",
      transactionDate: "",
      accountNumber: "",
      AccountName: "",
      transferbankaccount: "",
      amount: "",
      document: undefined,
    },
    mode: "onTouched",
  });

  const onSubmit = async (data: z.infer<typeof formSchema>) => {
    if (!state) {
      toast.error("Missing required data. Please start the process again.", {
        position: "top-right",
        autoClose: 5000,
        hideProgressBar: false,
        closeOnClick: true,
        pauseOnHover: true,
        draggable: true,
        progress: undefined,
      });
      return;
    }

    setIsLoading(true); // Set loading to true when submission starts
    try {
      const submissionData = {
        ...state.formData, // Contains: availableFrom, availableTo, timezone, etc.
        ...state.billingData, // Contains: city, zipcode, country, state, street
        amount: data.amount,
        transactionId: data.transactionId,
        bankHolderName: data.AccountName,
        accountNumber: data.accountNumber || null,
        bankName: data.transferbankaccount,
        screenshot: data.document,
        transactionDate: data.transactionDate,
        transferedAccNo: data.transferbankaccount,
        currency: "USD", // You can make this dynamic if needed
      };

      const response = await PostBankTransfer(submissionData);
      console.log("banktransfer data", response);

      if (response.status === 1) {
        toast.success(response.message);
        navigate("/dashboard/subscription-success"); // Or your success route
      } else {
        toast.error("Submission failed. Please try again.");
      }
    } catch (error) {
      console.error("Bank transfer submission error:", error);
      toast.error("An error occurred. Please try again.");
    } finally {
      setIsLoading(false); // Reset loading state after submission
    }
  };

  return (
    <div className="min-h-screen flex flex-col ">
      {/* Header */}
      <header className=" py-4">
        <div className="container mx-auto px-4 flex justify-end items-center">
          <Button variant={"gradient"} className="px-6 py-3">
            <a
              href="https://dashboard.skydo.com/accounts/macgence?location=usa¤cy=native"
              target="_blank"
              rel="noopener noreferrer"
              className="text-white hover:no-underline"
            >
              Account Details
            </a>
          </Button>
        </div>
      </header>

      {/* Main Content */}
      <main className="flex-1 container mx-auto px-4 py-8">
        <div className="max-w-3xl mx-auto bg-white rounded-xl shadow-md p-8">
          <h2 className="text-2xl font-bold mb-8 text-center text-gray-800">
            Bank Transfer Details
          </h2>

          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
              {/* First Row */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Transaction ID */}
                <FormField
                  control={form.control}
                  name="transactionId"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-gray-700 font-medium">
                        Transaction ID
                      </FormLabel>
                      <FormControl>
                        <div className="border-gradient rounded-lg p-[2px]">
                          <Input
                            placeholder="Enter Transaction ID"
                            {...field}
                            className="bg-white rounded-md h-11 w-full border-none focus-visible:ring-0"
                            onBlur={field.onBlur}
                          />
                        </div>
                      </FormControl>
                      <FormMessage className="text-red-500 text-sm" />
                    </FormItem>
                  )}
                />

                {/* Transaction Date */}
                <FormField
                  control={form.control}
                  name="transactionDate"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-gray-700 font-medium">
                        Transaction Date
                      </FormLabel>
                      <FormControl>
                        <div className="border-gradient rounded-lg p-[2px]">
                          <Input
                            type="date"
                            placeholder="Select Transaction Date"
                            {...field}
                            value={field.value || ""}
                            onChange={(e) => field.onChange(e.target.value)}
                            className="bg-white rounded-md h-11 w-full border-none focus-visible:ring-0"
                            onBlur={field.onBlur}
                          />
                        </div>
                      </FormControl>
                      <FormMessage className="text-red-500 text-sm" />
                    </FormItem>
                  )}
                />
              </div>

              {/* Second Row */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Account Number - Now Optional */}
                <FormField
                  control={form.control}
                  name="accountNumber"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-gray-700 font-medium">
                        Account Number (Optional)
                      </FormLabel>
                      <FormControl>
                        <div className="border-gradient rounded-lg p-[2px]">
                          <Input
                            placeholder="Enter Account Number (Optional)"
                            {...field}
                            className="bg-white rounded-md h-11 w-full border-none focus-visible:ring-0"
                            onBlur={field.onBlur}
                          />
                        </div>
                      </FormControl>
                      <FormMessage className="text-red-500 text-sm" />
                    </FormItem>
                  )}
                />

                {/* Account Name */}
                <FormField
                  control={form.control}
                  name="AccountName"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-gray-700 font-medium">
                        Account Name
                      </FormLabel>
                      <FormControl>
                        <div className="border-gradient rounded-lg p-[2px]">
                          <Input
                            placeholder="Enter Account Name"
                            {...field}
                            className="bg-white rounded-md h-11 w-full border-none focus-visible:ring-0"
                            onBlur={field.onBlur}
                          />
                        </div>
                      </FormControl>
                      <FormMessage className="text-red-500 text-sm" />
                    </FormItem>
                  )}
                />
              </div>

              {/* Third Row */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Transfer Bank Account */}
                <FormField
                  control={form.control}
                  name="transferbankaccount"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-gray-700 font-medium">
                        Transfer Bank Account
                      </FormLabel>
                      <FormControl>
                        <div className="border-gradient rounded-lg p-[2px]">
                          <Input
                            placeholder="Enter Transfer Bank Account"
                            {...field}
                            className="bg-white rounded-md h-11 w-full border-none focus-visible:ring-0"
                            onBlur={field.onBlur}
                          />
                        </div>
                      </FormControl>
                      <FormMessage className="text-red-500 text-sm" />
                    </FormItem>
                  )}
                />

                {/* Transfer Amount */}
                <FormField
                  control={form.control}
                  name="amount"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-gray-700 font-medium">
                        Amount
                      </FormLabel>
                      <FormControl>
                        <div className="border-gradient rounded-lg p-[2px]">
                          <Input
                            placeholder="Enter Transfer Amount"
                            {...field}
                            className="bg-white rounded-md h-11 w-full border-none focus-visible:ring-0"
                            onBlur={field.onBlur}
                          />
                        </div>
                      </FormControl>
                      <FormMessage className="text-red-500 text-sm" />
                    </FormItem>
                  )}
                />
              </div>

              {/* Document Upload */}
              <FormField
                control={form.control}
                name="document"
                render={({ field, fieldState }) => (
                  <FormItem>
                    <FormLabel className="text-gray-700 font-medium">
                      Upload Document (Proof of Transfer)
                      <span className="text-gray-500 text-sm ml-2">
                        (JPEG, PNG, JPG, or PDF, max 5MB)
                      </span>
                    </FormLabel>
                    <FormControl>
                      <div className="border-gradient flex justify-center items-center rounded-lg p-[2px]">
                        <FileUpload
                          onChange={(file) => {
                            field.onChange(file);
                            setIsFileDialogOpen(false);
                          }}
                          value={field.value}
                          accept=".jpg,.jpeg,.png,.pdf"
                        />
                      </div>
                    </FormControl>
                    {!isFileDialogOpen && fieldState.error && (
                      <FormMessage className="text-red-500 text-sm">
                        {fieldState.error.message}
                      </FormMessage>
                    )}
                  </FormItem>
                )}
              />

              {/* Submit Button with Loading State */}
              <div className="flex justify-center pt-4">
                <Button
                  variant={"gradient"}
                  type="submit"
                  className="px-12 py-4 text-lg font-medium flex items-center justify-center"
                  disabled={isLoading} // Disable button during loading
                >
                  {isLoading ? (
                    <>
                      <svg
                        className="animate-spin h-5 w-5 mr-2 text-white"
                        xmlns="http://www.w3.org/2000/svg"
                        fill="none"
                        viewBox="0 0 24 24"
                      >
                        <circle
                          className="opacity-25"
                          cx="12"
                          cy="12"
                          r="10"
                          stroke="currentColor"
                          strokeWidth="4"
                        ></circle>
                        <path
                          className="opacity-75"
                          fill="currentColor"
                          d="M4 12a8 8 0 018-8v4a4 4 0 00-4 4H4z"
                        ></path>
                      </svg>
                      Submitting...
                    </>
                  ) : (
                    "Submit"
                  )}
                </Button>
              </div>
            </form>
          </Form>
        </div>
      </main>
    </div>
  );
};

export default AddBankTransfer;
