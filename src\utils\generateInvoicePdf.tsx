import { jsPDF } from "jspdf";
import logo from "@/assets/darklogo.png";

interface InvoiceData {
  provider: string;
  paymentId: string;
  amount: string;
  status: string;
  paymentMethod: string;
  date: string;
  customerName: string;
  customerEmail: string;
  packageName: string;
  price: string;
}

export const generateInvoicePdf = (invoiceData: InvoiceData) => {
  const doc = new jsPDF();

  // Add company logo (top left)
  try {
    doc.addImage(logo, 'PNG', 20, 15, 30, 12);
  } catch (error) {
    console.error("Error adding logo to PDF:", error);
  }

  // Company name and details (left side)
  doc.setFontSize(16);
  doc.setTextColor(0, 0, 0);
  doc.setFont("helvetica", "bold");
  doc.text("Macgence", 20, 35);

  doc.setFontSize(9);
  doc.setFont("helvetica", "normal");
  doc.setTextColor(0, 0, 0);
  doc.text("Macgence Technologies Private Limited", 20, 42);
  doc.text("CN UF22000UP2022PTC164392", 20, 47);
  doc.text("7th Floor, Platina Heights C - 24 Sector 62", 20, 52);
  doc.text("Noida Uttar Pradesh 201301", 20, 57);
  doc.text("India", 20, 62);
  doc.text("GSTIN:09AAPCH4735A1Z9", 20, 67);

  // TAX INVOICE title (right side)
  doc.setFontSize(24);
  doc.setTextColor(0, 0, 0);
  doc.setFont("helvetica", "bold");
  doc.text("TAX INVOICE", 140, 25);

  // Invoice details (right side)
  doc.setFontSize(10);
  doc.setFont("helvetica", "normal");
  doc.text(`Invoice# INV25/26-00021`, 140, 35);
  doc.text(`Balance Due`, 140, 45);
  doc.text(`$${invoiceData.amount}`, 140, 55);

  // Invoice Date and Terms (right side)
  doc.text(`Invoice Date :`, 140, 70);
  doc.text(`${new Date(invoiceData.date).toLocaleDateString('en-GB')}`, 175, 70);
  doc.text(`Terms :`, 140, 80);
  doc.text(`Net 4`, 175, 80);
  doc.text(`Due Date :`, 140, 90);
  doc.text(`${new Date(new Date(invoiceData.date).getTime() + 4 * 24 * 60 * 60 * 1000).toLocaleDateString('en-GB')}`, 175, 90);

  // Bill To section
  doc.setFontSize(12);
  doc.setFont("helvetica", "bold");
  doc.text("Bill To", 20, 105);

  doc.setFontSize(10);
  doc.setFont("helvetica", "normal");
  doc.text(invoiceData.customerName, 20, 115);
  doc.text(invoiceData.customerEmail, 20, 125);
  doc.text("Israel", 20, 135); // Default location as shown in image

  // Table header
  const tableTop = 150;

  // Table header background (light blue)
  doc.setFillColor(173, 216, 230);
  doc.rect(20, tableTop, 170, 12, 'F');

  // Table header borders
  doc.setDrawColor(0, 0, 0);
  doc.setLineWidth(0.5);
  doc.rect(20, tableTop, 170, 12);

  // Table header text
  doc.setTextColor(0, 0, 0);
  doc.setFontSize(10);
  doc.setFont("helvetica", "bold");
  doc.text("#", 25, tableTop + 8);
  doc.text("Item & Description", 35, tableTop + 8);
  doc.text("HSN/SAC", 100, tableTop + 8);
  doc.text("Qty", 125, tableTop + 8);
  doc.text("Rate", 145, tableTop + 8);
  doc.text("Amount", 170, tableTop + 8);

  // Table content row
  doc.setFillColor(255, 255, 255);
  doc.rect(20, tableTop + 12, 170, 15, 'F');
  doc.rect(20, tableTop + 12, 170, 15);

  // Vertical lines for table columns
  doc.line(30, tableTop, 30, tableTop + 27);
  doc.line(95, tableTop, 95, tableTop + 27);
  doc.line(120, tableTop, 120, tableTop + 27);
  doc.line(140, tableTop, 140, tableTop + 27);
  doc.line(160, tableTop, 160, tableTop + 27);

  doc.setTextColor(0, 0, 0);
  doc.setFont("helvetica", "normal");
  doc.text("1", 25, tableTop + 20);
  doc.text("B2B Invoices dataset delivery", 35, tableTop + 20);
  doc.text("(OTS Inventory)", 35, tableTop + 25);
  doc.text("998313", 100, tableTop + 20);
  doc.text("1,000.00", 125, tableTop + 20);
  doc.text("0.30", 145, tableTop + 20);
  doc.text("300.00", 170, tableTop + 20);

  // Totals section
  const totalsTop = tableTop + 40;

  doc.setFontSize(10);
  doc.setFont("helvetica", "normal");
  doc.text("Sub Total", 130, totalsTop);
  doc.text("300.00", 170, totalsTop);

  doc.setFont("helvetica", "bold");
  doc.text("Total", 130, totalsTop + 10);
  doc.text(`$${invoiceData.amount}`, 170, totalsTop + 10);

  doc.text("Balance Due", 130, totalsTop + 20);
  doc.text(`$${invoiceData.amount}`, 170, totalsTop + 20);

  // Total in words
  doc.setFontSize(9);
  doc.setFont("helvetica", "normal");
  doc.text("Total In Words:", 130, totalsTop + 35);
  doc.setFont("helvetica", "italic");
  doc.text("United States Dollar Three", 130, totalsTop + 42);
  doc.text("Hundred", 130, totalsTop + 47);

  // Footer section
  doc.setFontSize(9);
  doc.setFont("helvetica", "normal");
  doc.text("Thanks for your business.", 20, 240);
  doc.text("PayPal account: <EMAIL>", 20, 247);

  doc.text("Account Holder Name : Macgence Technologies Private Limited", 20, 257);
  doc.text("Payment Method : SWIFT (International Wire)", 20, 262);
  doc.text("IBAN : **********************", 20, 267);
  doc.text("BIC/SWIFT : TCCLGB3L", 20, 272);
  doc.text("Bank Name : The Currency Cloud Limited", 20, 277);
  doc.text("Bank Address : 12, Steward Street, The Steward Building, London, E1 6FQ, Great Britain, United Kingdom", 20, 282);

  return doc;
};
