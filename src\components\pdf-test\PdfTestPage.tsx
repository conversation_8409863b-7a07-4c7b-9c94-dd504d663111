import React from 'react';
import { DownloadAttendanceButton } from '@/components/attendance/DownloadAttendanceButton';
import { DownloadInvoiceButton } from '@/features/clientdashboard/billing/transaction/DownloadInvoiceButton';

export const PdfTestPage: React.FC = () => {
  // Sample invoice data
  const sampleInvoiceData = {
    paymentId: "PAY123456789",
    customerName: "Avi Bar Moha",
    customerEmail: "<EMAIL>",
    packageName: "Premium Data Annotation Package",
    amount: 300,
    status: "Completed",
    paymentMethod: "PayPal",
    date: new Date().toISOString()
  };

  return (
    <div className="min-h-screen bg-gray-50 p-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold text-gray-800 mb-8 text-center">
          PDF Generation Test Page
        </h1>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          {/* Invoice PDF Test */}
          <div className="bg-white p-6 rounded-lg shadow-lg border">
            <h2 className="text-xl font-bold text-blue-600 mb-4">
              📄 Tax Invoice PDF
            </h2>
            <div className="space-y-3 text-sm text-gray-600 mb-6">
              <p><strong>Features:</strong></p>
              <ul className="list-disc list-inside space-y-1">
                <li>Professional Macgence branding</li>
                <li>Complete company details & GSTIN</li>
                <li>Beautiful table with HSN/SAC codes</li>
                <li>Banking & payment information</li>
                <li>Real client data integration</li>
                <li>Responsive design with gradients</li>
              </ul>
              
              <div className="mt-4 p-3 bg-blue-50 rounded">
                <p className="text-xs"><strong>Sample Data:</strong></p>
                <p>Customer: {sampleInvoiceData.customerName}</p>
                <p>Amount: ${sampleInvoiceData.amount}</p>
                <p>Package: {sampleInvoiceData.packageName}</p>
              </div>
            </div>
            
            <DownloadInvoiceButton 
              paymentId={sampleInvoiceData.paymentId}
              customerName={sampleInvoiceData.customerName}
              customerEmail={sampleInvoiceData.customerEmail}
              packageName={sampleInvoiceData.packageName}
              amount={sampleInvoiceData.amount}
              status={sampleInvoiceData.status}
              paymentMethod={sampleInvoiceData.paymentMethod}
              date={sampleInvoiceData.date}
            />
          </div>

          {/* Attendance PDF Test */}
          <div className="bg-white p-6 rounded-lg shadow-lg border">
            <h2 className="text-xl font-bold text-green-600 mb-4">
              📊 Attendance Report PDF
            </h2>
            <div className="space-y-3 text-sm text-gray-600 mb-6">
              <p><strong>Features:</strong></p>
              <ul className="list-disc list-inside space-y-1">
                <li>Professional employee report layout</li>
                <li>Monthly attendance summary</li>
                <li>Color-coded status indicators</li>
                <li>Performance analytics & charts</li>
                <li>Detailed daily records table</li>
                <li>Company branding & footer</li>
              </ul>
              
              <div className="mt-4 p-3 bg-green-50 rounded">
                <p className="text-xs"><strong>Sample Data:</strong></p>
                <p>Employee: John Doe (EMP001)</p>
                <p>Department: Data Annotation</p>
                <p>Period: Current Month</p>
                <p>Records: ~20 working days</p>
              </div>
            </div>
            
            <DownloadAttendanceButton />
          </div>
        </div>

        {/* Instructions */}
        <div className="mt-8 bg-white p-6 rounded-lg shadow-lg border">
          <h3 className="text-lg font-bold text-gray-800 mb-4">
            🚀 How to Use
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 text-sm text-gray-600">
            <div>
              <h4 className="font-semibold text-blue-600 mb-2">Invoice PDF:</h4>
              <ul className="list-disc list-inside space-y-1">
                <li>Click "Download" button in billing transactions</li>
                <li>PDF generates with real client data</li>
                <li>Professional tax invoice format</li>
                <li>Includes all Macgence company details</li>
                <li>Banking information for payments</li>
              </ul>
            </div>
            <div>
              <h4 className="font-semibold text-green-600 mb-2">Attendance PDF:</h4>
              <ul className="list-disc list-inside space-y-1">
                <li>Generate monthly attendance reports</li>
                <li>Includes employee performance metrics</li>
                <li>Color-coded status indicators</li>
                <li>Professional HR department format</li>
                <li>Detailed daily attendance records</li>
              </ul>
            </div>
          </div>
        </div>

        {/* Technical Details */}
        <div className="mt-8 bg-gray-100 p-6 rounded-lg">
          <h3 className="text-lg font-bold text-gray-800 mb-4">
            ⚙️ Technical Implementation
          </h3>
          <div className="text-sm text-gray-600 space-y-2">
            <p><strong>Technology Stack:</strong></p>
            <ul className="list-disc list-inside space-y-1 ml-4">
              <li><strong>react-to-pdf + html2canvas:</strong> Converts HTML/CSS to PDF</li>
              <li><strong>Tailwind CSS:</strong> Beautiful responsive styling</li>
              <li><strong>React Components:</strong> Reusable template system</li>
              <li><strong>TypeScript:</strong> Type-safe data handling</li>
            </ul>
            
            <p className="mt-4"><strong>Benefits:</strong></p>
            <ul className="list-disc list-inside space-y-1 ml-4">
              <li>Easy to customize with CSS/Tailwind</li>
              <li>Pixel-perfect design control</li>
              <li>Responsive layouts</li>
              <li>Real data integration</li>
              <li>Professional output quality</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
};
